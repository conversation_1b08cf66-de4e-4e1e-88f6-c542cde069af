"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, FileText, Image as ImageIcon, Download, Eye } from 'lucide-react'


interface FilePreviewModalProps {
  isOpen: boolean
  onClose: () => void
  file: File | null
  fileType: 'image' | 'document' | 'audio' | 'video'
  preview?: string
  content?: string
}

export function FilePreviewModal({
  isOpen,
  onClose,
  file,
  fileType,
  preview,
  content
}: FilePreviewModalProps) {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [pdfPages, setPdfPages] = useState<string[]>([])
  const [pdfCanvases, setPdfCanvases] = useState<string[]>([])
  const [htmlContent, setHtmlContent] = useState<string>('')
  const [isClient, setIsClient] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [zoomLevel, setZoomLevel] = useState(1)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (isOpen && file && isClient) {
      loadFileContent()
    }
  }, [isOpen, file, isClient])

  // Keyboard zoom for PDF, Word, and Text
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen || (!file?.type.includes('pdf') && !file?.type.includes('word') && !file?.type.includes('document') && file?.type !== 'text/plain')) return

      if (e.key === '=' || e.key === '+') {
        e.preventDefault()
        handleZoom(0.2)
      } else if (e.key === '-') {
        e.preventDefault()
        handleZoom(-0.2)
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, file?.type, zoomLevel])

  const loadFileContent = async () => {
    if (!file) return

    setIsLoading(true)
    setError('')
    setPreviewContent('')
    setPdfPages([])
    setPdfCanvases([])
    setHtmlContent('')
    setCurrentPage(1)
    setTotalPages(0)
    setZoomLevel(1)

    try {
      if (fileType === 'image') {
        // For images, use the preview URL directly
        setPreviewContent(preview || '')
      } else if (file.type === 'text/plain' || file.type === 'text/csv') {
        // For text files, read directly with original formatting
        const text = await readTextFile(file)
        setPreviewContent(text)
      } else if (file.type === 'application/pdf') {
        // For PDF files, render as visual pages
        await loadPdfContentAsImages(file)
      } else if (file.type.includes('word') || file.type.includes('document')) {
        // For Word documents, convert to HTML with formatting
        await loadWordContentAsImages(file)
      } else {
        // Fallback to existing content
        setPreviewContent(content || 'Không thể xem trước loại file này.')
      }
    } catch (err) {
      setError(`Lỗi khi tải nội dung file: ${err}`)
    } finally {
      setIsLoading(false)
    }
  }

  const readTextFile = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('Không thể đọc file text'))
      reader.readAsText(file, 'utf-8')
    })
  }

  const loadPdfContentAsImages = async (file: File) => {
    try {
      // Dynamic import PDF.js only when needed
      const pdfjsLib = await import('pdfjs-dist')

      // Use worker from public folder
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf-worker/pdf.worker.min.js'

      const arrayBuffer = await file.arrayBuffer()
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
      const canvases: string[] = []

      // Render first 5 pages to avoid performance issues
      const maxPages = Math.min(pdf.numPages, 5)

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        const page = await pdf.getPage(pageNum)

        // Set up canvas for rendering
        const viewport = page.getViewport({ scale: 1.5 })
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')

        if (!context) {
          throw new Error('Cannot get canvas context')
        }

        canvas.height = viewport.height
        canvas.width = viewport.width

        // Render page to canvas
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        }

        await page.render(renderContext).promise

        // Convert canvas to data URL
        const dataUrl = canvas.toDataURL('image/png')
        canvases.push(dataUrl)
      }

      setPdfCanvases(canvases)
      setTotalPages(canvases.length)

      if (pdf.numPages > 5) {
        setPreviewContent(`Tải ${maxPages} trang đầu tiên. Tổng cộng ${pdf.numPages} trang.`)
      } else {
        setPreviewContent(`Tải tất cả ${pdf.numPages} trang.`)
      }
    } catch (err) {
      throw new Error(`Không thể đọc PDF: ${err}`)
    }
  }

  const loadWordContentAsImages = async (file: File) => {
    try {
      // Method 1: Try to render Word document as images for 100% fidelity
      // This approach converts each page to an image like PDF

      const docxPreview = await import('docx-preview')
      const html2canvas = await import('html2canvas')

      const arrayBuffer = await file.arrayBuffer()

      // Create a hidden container for rendering with enhanced styling
      const hiddenContainer = document.createElement('div')
      hiddenContainer.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        width: 8.5in;
        background: white;
        font-family: 'Times New Roman', 'Calibri', serif;
        visibility: hidden;
        pointer-events: none;
        zoom: 1;
      `

      // Add comprehensive CSS for better rendering
      const styleSheet = document.createElement('style')
      styleSheet.textContent = `
        .docx-image-render * {
          box-sizing: border-box !important;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        .docx-image-render section.docx {
          background: white !important;
          width: 8.5in !important;
          min-height: 11in !important;
          padding: 0 !important;
          margin: 0 !important;
          border: none !important;
          page-break-after: always !important;
          position: relative !important;
          overflow: visible !important;
        }

        .docx-image-render .docx-wrapper {
          width: 100% !important;
          height: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
        }

        .docx-image-render table {
          border-collapse: collapse !important;
          width: 100% !important;
        }

        .docx-image-render td, .docx-image-render th {
          border: 1px solid #000 !important;
          padding: 4px !important;
        }

        .docx-image-render img {
          max-width: 100% !important;
          height: auto !important;
          display: block !important;
        }

        .docx-image-render p {
          margin: 0 !important;
          padding: 0 !important;
          line-height: 1.15 !important;
        }

        .docx-image-render div {
          position: relative !important;
        }
      `
      hiddenContainer.appendChild(styleSheet)
      document.body.appendChild(hiddenContainer)

      try {
        // Render document with maximum fidelity settings
        await docxPreview.renderAsync(arrayBuffer, hiddenContainer, undefined, {
          className: "docx-image-render",
          inWrapper: true,
          ignoreWidth: false,
          ignoreHeight: false,
          ignoreFonts: false,
          breakPages: true,
          ignoreLastRenderedPageBreak: false,
          experimental: true, // Enable experimental features for better rendering
          trimXmlDeclaration: true,
          useBase64URL: true,
          renderHeaders: true,
          renderFooters: true,
          renderFootnotes: true,
          renderEndnotes: true,
          renderChanges: false,
          renderComments: false,
          debug: false
        })

        // Find all page sections
        const pages = hiddenContainer.querySelectorAll('section.docx')
        const pageImages: string[] = []

        if (pages.length > 0) {
          // Convert each page to image
          for (let i = 0; i < Math.min(pages.length, 10); i++) { // Limit to 10 pages for performance
            const page = pages[i] as HTMLElement

            // Style the page for maximum fidelity rendering
            page.style.cssText += `
              background: white;
              width: 8.5in;
              min-height: 11in;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              font-family: 'Times New Roman', 'Calibri', serif;
              position: relative;
              display: block;
              overflow: visible;
              border: none;
              outline: none;
            `

            // Ensure all child elements are visible and properly styled
            const allElements = page.querySelectorAll('*')
            allElements.forEach(el => {
              const element = el as HTMLElement
              element.style.visibility = 'visible'
              element.style.opacity = '1'
              element.style.display = element.style.display || 'block'
            })

            try {
              // Wait for fonts and images to load
              await new Promise(resolve => setTimeout(resolve, 500))

              // Convert page to canvas with maximum quality settings
              const canvas = await html2canvas.default(page, {
                scale: 3, // Very high DPI for maximum quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: page.offsetWidth || 816, // 8.5in at 96dpi
                height: page.offsetHeight || 1056, // 11in at 96dpi
                scrollX: 0,
                scrollY: 0,
                logging: false,
                removeContainer: false,
                foreignObjectRendering: true,
                imageTimeout: 5000,
                onclone: (clonedDoc) => {
                  // Ensure all styles are preserved in the clone
                  const clonedPage = clonedDoc.querySelector('section.docx') as HTMLElement
                  if (clonedPage) {
                    clonedPage.style.cssText += `
                      background: white !important;
                      width: 8.5in !important;
                      min-height: 11in !important;
                      font-family: 'Times New Roman', 'Calibri', serif !important;
                    `
                  }
                }
              })

              // Convert canvas to data URL
              const imageData = canvas.toDataURL('image/png', 0.95)
              pageImages.push(imageData)
            } catch (canvasErr) {
              console.warn(`Failed to render page ${i + 1} as image:`, canvasErr)
            }
          }
        }

        // Clean up hidden container
        document.body.removeChild(hiddenContainer)

        if (pageImages.length > 0) {
          // Use images like PDF
          setPdfCanvases(pageImages)
          setTotalPages(pageImages.length)
          setPreviewContent(`Word document - ${pageImages.length} trang - Rendered as images`)
          return
        } else {
          // Debug: Log the rendered HTML to see what's missing
          console.log('No images generated. Rendered HTML:', hiddenContainer.innerHTML)
        }

      } catch (renderErr) {
        console.warn('Failed to render as images, falling back to HTML:', renderErr)
        if (document.body.contains(hiddenContainer)) {
          document.body.removeChild(hiddenContainer)
        }
      }

      // Try alternative approach: iframe-based rendering for complex layouts
      try {
        console.log('Trying iframe-based rendering for better fidelity...')

        const iframe = document.createElement('iframe')
        iframe.style.cssText = `
          position: absolute;
          top: -10000px;
          left: -10000px;
          width: 8.5in;
          height: 11in;
          border: none;
          visibility: hidden;
        `
        document.body.appendChild(iframe)

        // Render in iframe for better isolation
        const iframeContainer = document.createElement('div')
        await docxPreview.renderAsync(arrayBuffer, iframeContainer, undefined, {
          className: "docx-iframe-render",
          inWrapper: true,
          ignoreWidth: false,
          ignoreHeight: false,
          ignoreFonts: false,
          breakPages: true,
          experimental: true,
          trimXmlDeclaration: true,
          useBase64URL: true,
          renderHeaders: true,
          renderFooters: true,
          renderFootnotes: true,
          renderEndnotes: true,
          debug: false
        })

        if (iframe.contentDocument) {
          iframe.contentDocument.body.innerHTML = `
            <style>
              body { margin: 0; padding: 0; font-family: 'Times New Roman', serif; }
              section.docx {
                width: 8.5in;
                min-height: 11in;
                background: white;
                margin: 0;
                padding: 1in;
                box-sizing: border-box;
              }
            </style>
            ${iframeContainer.innerHTML}
          `

          // Wait for iframe to load
          await new Promise(resolve => setTimeout(resolve, 1000))

          const iframePages = iframe.contentDocument.querySelectorAll('section.docx')
          const iframeImages: string[] = []

          for (let i = 0; i < Math.min(iframePages.length, 5); i++) {
            const page = iframePages[i] as HTMLElement
            try {
              const canvas = await html2canvas.default(page, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 816,
                height: 1056
              })

              const imageData = canvas.toDataURL('image/png', 0.9)
              iframeImages.push(imageData)
            } catch (err) {
              console.warn(`Failed to render iframe page ${i + 1}:`, err)
            }
          }

          document.body.removeChild(iframe)

          if (iframeImages.length > 0) {
            setPdfCanvases(iframeImages)
            setTotalPages(iframeImages.length)
            setPreviewContent(`Word document - ${iframeImages.length} trang - Iframe rendered`)
            return
          }
        }

        document.body.removeChild(iframe)

      } catch (iframeErr) {
        console.warn('Iframe rendering also failed:', iframeErr)
      }

      // Fallback: Enhanced HTML rendering
      const container = document.createElement('div')
      await docxPreview.renderAsync(arrayBuffer, container, undefined, {
        className: "docx-preview-container",
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        renderHeaders: true,
        renderFooters: true,
        renderFootnotes: true,
        renderEndnotes: true,
        debug: false
      })

      const renderedHtml = container.innerHTML
      const finalHtml = `
        <div style="
          background: #f0f0f0;
          padding: 20px;
          min-height: 100vh;
          font-family: 'Calibri', 'Times New Roman', serif;
        ">
          ${renderedHtml}
        </div>
        <style>
          .docx-preview-container {
            max-width: none !important;
            margin: 0 auto;
          }

          .docx-preview-container .docx {
            background: white;
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            border: 1px solid #e0e0e0;
            margin: 0 auto 20px auto;
            max-width: 8.5in;
            font-family: 'Calibri', 'Times New Roman', serif !important;
          }

          .docx-preview-container .docx * {
            font-family: 'Calibri', 'Times New Roman', serif !important;
          }

          .docx-preview-container section.docx {
            margin-bottom: 20px;
          }
        </style>
      `

      setHtmlContent(finalHtml)
      const pageCount = (container.querySelectorAll('section.docx') || []).length
      setTotalPages(pageCount)
      setPreviewContent(`Word document - ${pageCount} trang - Enhanced HTML`)

    } catch (err) {
      console.error('Error with Word rendering, falling back to mammoth:', err)

      // Final fallback to mammoth
      try {
        const mammothLib = await import('mammoth')
        const mammoth = mammothLib.default || mammothLib
        const arrayBuffer = await file.arrayBuffer()
        const result = await mammoth.convertToHtml({ arrayBuffer })

        const fallbackHtml = `
          <div style="
            background: #f0f0f0;
            padding: 20px;
            min-height: 100vh;
          ">
            <div style="
              background: white;
              width: 8.5in;
              min-height: 11in;
              margin: 0 auto;
              padding: 1in;
              box-shadow: 0 8px 24px rgba(0,0,0,0.12);
              border: 1px solid #e0e0e0;
              font-family: 'Calibri', 'Times New Roman', serif;
              font-size: 11pt;
              line-height: 1.15;
              color: #000000;
            ">
              ${result.value}
            </div>
          </div>
        `

        setHtmlContent(fallbackHtml)
        setPreviewContent(`Word document - Fallback mode`)
      } catch (fallbackErr) {
        throw new Error(`Không thể đọc Word document: ${fallbackErr}`)
      }
    }
  }

  const downloadFile = () => {
    if (!file) return

    const url = URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }



  // Zoom functions
  const handleZoom = (delta: number) => {
    const newZoom = Math.max(0.5, Math.min(3, zoomLevel + delta))
    setZoomLevel(newZoom)
  }

  const handleWheel = (e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault()
      const delta = e.deltaY > 0 ? -0.1 : 0.1
      handleZoom(delta)
    }
  }

  // Throttle scroll events for better performance
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Track scroll position to update current page
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    // Clear previous timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // Throttle scroll events
    scrollTimeoutRef.current = setTimeout(() => {
      // Debug logging
      console.log('Scroll event - totalPages:', totalPages, 'currentPage:', currentPage)

      if (totalPages <= 1) return

      const container = e.currentTarget

      // Find all page elements
      const pageElements = container.querySelectorAll('.page-container, section.docx')
      console.log('Found page elements:', pageElements.length)

      if (pageElements.length === 0) return

      // Calculate which page is currently visible
      let visiblePage = 1
      let maxVisibleArea = 0

      pageElements.forEach((pageElement, index) => {
        const element = pageElement as HTMLElement
        const rect = element.getBoundingClientRect()
        const containerRect = container.getBoundingClientRect()

        // Calculate visible area of this page
        const visibleTop = Math.max(rect.top, containerRect.top)
        const visibleBottom = Math.min(rect.bottom, containerRect.bottom)
        const visibleHeight = Math.max(0, visibleBottom - visibleTop)

        if (visibleHeight > maxVisibleArea) {
          maxVisibleArea = visibleHeight
          visiblePage = index + 1
        }
      })

      console.log('Calculated visible page:', visiblePage, 'current:', currentPage)

      if (visiblePage !== currentPage) {
        console.log('Updating current page from', currentPage, 'to', visiblePage)
        setCurrentPage(visiblePage)
      }
    }, 100) // 100ms throttle
  }

  // Navigate to specific page
  const goToPage = (pageNumber: number) => {
    if (pageNumber < 1 || pageNumber > totalPages) return

    const container = document.querySelector('.h-full.overflow-y-auto.overflow-x-hidden') as HTMLElement
    if (!container) return

    const pageElements = container.querySelectorAll('.page-container, section.docx')
    const targetPage = pageElements[pageNumber - 1] as HTMLElement

    if (targetPage) {
      targetPage.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  const getFileIcon = () => {
    if (fileType === 'image') return <ImageIcon className="h-5 w-5" />
    return <FileText className="h-5 w-5" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Don't render on server-side
  if (!isClient) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl w-[90vw] h-[85vh] flex flex-col p-0 overflow-hidden">
        <DialogHeader className="flex-shrink-0 p-4 pb-3 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getFileIcon()}
              <div>
                <DialogTitle className="text-left">{file?.name}</DialogTitle>
                <p className="text-sm text-muted-foreground">
                  {file && formatFileSize(file.size)} • {file?.type}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Zoom Controls for PDF, Word, and Text */}
              {((file?.type === 'application/pdf' && pdfCanvases.length > 0) ||
                ((file?.type.includes('word') || file?.type.includes('document')) && (htmlContent || pdfCanvases.length > 0)) ||
                (file?.type === 'text/plain' && previewContent)) && (
                <div className="flex items-center gap-2 mr-2">
                  {/* Debug info */}
                  <span className="text-xs text-gray-500 mr-2">
                    Debug: {totalPages} total, {currentPage} current
                  </span>

                  {(file?.type === 'application/pdf' || (file?.type.includes('word') && pdfCanvases.length > 0)) && totalPages > 0 ? (
                    <div className="flex items-center gap-1">
                      {totalPages > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="h-6 w-6 p-0"
                        >
                          ←
                        </Button>
                      )}
                      <span className="text-sm font-medium min-w-[60px] text-center">
                        Trang {currentPage}/{totalPages}
                      </span>
                      {totalPages > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="h-6 w-6 p-0"
                        >
                          →
                        </Button>
                      )}
                    </div>
                  ) : (file?.type.includes('word') || file?.type.includes('document')) && htmlContent && totalPages > 0 ? (
                    <span className="text-sm font-medium">
                      Trang {currentPage}/{totalPages} - HTML mode
                    </span>
                  ) : (
                    <span className="text-sm font-medium">
                      {file?.type === 'text/plain' ? 'Text File' :
                       previewContent.includes('trang') ? previewContent.split(' - ')[1] : 'Document'}
                    </span>
                  )}
                  <div className="flex items-center gap-1 ml-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(-0.2)}
                      className="h-8 w-8 p-0"
                      title="Zoom out"
                    >
                      -
                    </Button>
                    <span className="text-xs min-w-[40px] text-center">
                      {Math.round(zoomLevel * 100)}%
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleZoom(0.2)}
                      className="h-8 w-8 p-0"
                      title="Zoom in"
                    >
                      +
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setZoomLevel(1)}
                      className="h-8 px-2 text-xs"
                      title="Reset zoom"
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={downloadFile}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Tải xuống
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span>Đang tải nội dung...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-red-600 mb-2">{error}</p>
                <Button variant="outline" onClick={loadFileContent}>
                  Thử lại
                </Button>
              </div>
            </div>
          ) : fileType === 'image' && previewContent ? (
            <div className="flex items-center justify-center h-full p-4">
              <img
                src={previewContent}
                alt={file?.name}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              />
            </div>
          ) : (file?.type === 'application/pdf' || (file?.type.includes('word') && pdfCanvases.length > 0)) && pdfCanvases.length > 0 ? (
            <div className="h-full overflow-y-auto overflow-x-hidden" onWheel={handleWheel} onScroll={handleScroll}>
              <div className="p-4">
                <div className="text-sm text-gray-600 mb-4 text-center bg-blue-50 p-2 rounded">
                  {previewContent} • Ctrl + Scroll để zoom • Scroll để xem các trang
                </div>
                <div className="space-y-4">
                  {pdfCanvases.map((canvasData, index) => (
                    <div key={index} className="page-container border rounded overflow-hidden shadow-sm bg-white">
                      <div className="bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 border-b">
                        📄 Trang {index + 1}
                      </div>
                      <div className="p-3 flex justify-center bg-gray-50">
                        <img
                          src={canvasData}
                          alt={`Trang ${index + 1}`}
                          className="border shadow rounded"
                          style={{
                            width: zoomLevel === 1 ? '100%' : `${100 * zoomLevel}%`,
                            maxWidth: zoomLevel === 1 ? '100%' : 'none',
                            height: 'auto',
                            transition: 'width 0.2s ease',
                            objectFit: 'contain'
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (file?.type.includes('word') || file?.type.includes('document')) && htmlContent ? (
            <div className="h-full overflow-y-auto overflow-x-hidden" onWheel={handleWheel} onScroll={handleScroll}>
              <div className="p-4">
                <div className="text-sm text-gray-600 mb-4 text-center bg-blue-50 p-2 rounded">
                  {previewContent} • Ctrl + Scroll để zoom
                </div>
                <div className="flex justify-center">
                  <div
                    className="bg-white shadow-lg border"
                    style={{
                      width: `${100 * zoomLevel}%`,
                      maxWidth: zoomLevel === 1 ? '100%' : 'none',
                      transition: 'width 0.2s ease'
                    }}
                  >
                    <div
                      dangerouslySetInnerHTML={{ __html: htmlContent }}
                      style={{
                        transform: `scale(${zoomLevel})`,
                        transformOrigin: 'top center',
                        transition: 'transform 0.2s ease'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full overflow-y-auto overflow-x-hidden" onWheel={handleWheel}>
              <div className="p-4">
                <div className="text-sm text-gray-600 mb-4 text-center bg-gray-50 p-2 rounded">
                  📄 {file?.type === 'text/plain' ? 'Text Document' : 'File Content'} • Ctrl + Scroll để zoom
                </div>
                <div className="flex justify-center">
                  <div
                    className="bg-white border rounded shadow-sm"
                    style={{
                      width: `${100 * zoomLevel}%`,
                      maxWidth: zoomLevel === 1 ? '100%' : 'none',
                      transition: 'width 0.2s ease'
                    }}
                  >
                    <div className="p-4">
                      <pre
                        className="whitespace-pre-wrap text-sm leading-relaxed"
                        style={{
                          fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                          color: '#000',
                          margin: 0,
                          background: 'transparent',
                          transform: `scale(${zoomLevel})`,
                          transformOrigin: 'top left',
                          transition: 'transform 0.2s ease'
                        }}
                      >
                        {previewContent || 'Không có nội dung để hiển thị.'}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
