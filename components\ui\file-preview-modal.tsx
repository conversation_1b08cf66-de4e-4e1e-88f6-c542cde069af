"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, FileText, Image as ImageIcon, Download, Eye } from 'lucide-react'

interface FilePreviewModalProps {
  isOpen: boolean
  onClose: () => void
  file: File | null
  fileType: 'image' | 'document' | 'audio' | 'video'
  preview?: string
  content?: string
}

export function FilePreviewModal({
  isOpen,
  onClose,
  file,
  fileType,
  preview,
  content
}: FilePreviewModalProps) {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [pdfPages, setPdfPages] = useState<string[]>([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (isOpen && file && isClient) {
      loadFileContent()
    }
  }, [isOpen, file, isClient])

  const loadFileContent = async () => {
    if (!file) return

    setIsLoading(true)
    setError('')
    setPreviewContent('')
    setPdfPages([])

    try {
      if (fileType === 'image') {
        // For images, use the preview URL directly
        setPreviewContent(preview || '')
      } else if (file.type === 'text/plain' || file.type === 'text/csv') {
        // For text files, read directly
        const text = await readTextFile(file)
        setPreviewContent(text)
      } else if (file.type === 'application/pdf') {
        // For PDF files, use PDF.js for accurate rendering
        await loadPdfContent(file)
      } else if (file.type.includes('word') || file.type.includes('document')) {
        // For Word documents, use mammoth for accurate conversion
        await loadWordContent(file)
      } else {
        // Fallback to existing content
        setPreviewContent(content || 'Không thể xem trước loại file này.')
      }
    } catch (err) {
      setError(`Lỗi khi tải nội dung file: ${err}`)
    } finally {
      setIsLoading(false)
    }
  }

  const readTextFile = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('Không thể đọc file text'))
      reader.readAsText(file, 'utf-8')
    })
  }

  const loadPdfContent = async (file: File) => {
    try {
      // Dynamic import PDF.js only when needed
      const pdfjsLib = await import('pdfjs-dist')
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

      const arrayBuffer = await file.arrayBuffer()
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
      const pages: string[] = []

      // Render first 5 pages to avoid performance issues
      const maxPages = Math.min(pdf.numPages, 5)

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()

        // Extract text while preserving structure
        const textItems = textContent.items as any[]
        let pageText = ''
        let lastY = -1

        textItems.forEach((item) => {
          // Add line breaks for new lines
          if (lastY !== -1 && Math.abs(item.transform[5] - lastY) > 5) {
            pageText += '\n'
          }
          pageText += item.str + ' '
          lastY = item.transform[5]
        })

        pages.push(`--- Trang ${pageNum} ---\n${pageText.trim()}\n`)
      }

      if (pdf.numPages > 5) {
        pages.push(`\n... và ${pdf.numPages - 5} trang nữa`)
      }

      setPdfPages(pages)
      setPreviewContent(pages.join('\n\n'))
    } catch (err) {
      throw new Error(`Không thể đọc PDF: ${err}`)
    }
  }

  const loadWordContent = async (file: File) => {
    try {
      // Dynamic import mammoth only when needed
      const mammothLib = await import('mammoth')
      const mammoth = mammothLib.default || mammothLib

      const arrayBuffer = await file.arrayBuffer()
      const result = await mammoth.extractRawText({ arrayBuffer })

      // Preserve original formatting as much as possible
      let text = result.value

      // Clean up excessive whitespace while preserving structure
      text = text.replace(/\r\n/g, '\n')
      text = text.replace(/\n{3,}/g, '\n\n')
      text = text.trim()

      setPreviewContent(text)

      if (result.messages.length > 0) {
        console.warn('Word conversion warnings:', result.messages)
      }
    } catch (err) {
      throw new Error(`Không thể đọc Word document: ${err}`)
    }
  }

  const downloadFile = () => {
    if (!file) return

    const url = URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getFileIcon = () => {
    if (fileType === 'image') return <ImageIcon className="h-5 w-5" />
    return <FileText className="h-5 w-5" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Don't render on server-side
  if (!isClient) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getFileIcon()}
              <div>
                <DialogTitle className="text-left">{file?.name}</DialogTitle>
                <p className="text-sm text-muted-foreground">
                  {file && formatFileSize(file.size)} • {file?.type}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={downloadFile}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Tải xuống
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span>Đang tải nội dung...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-red-600 mb-2">{error}</p>
                <Button variant="outline" onClick={loadFileContent}>
                  Thử lại
                </Button>
              </div>
            </div>
          ) : fileType === 'image' && previewContent ? (
            <div className="flex items-center justify-center h-full p-4">
              <img
                src={previewContent}
                alt={file?.name}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              />
            </div>
          ) : (
            <ScrollArea className="h-full">
              <div className="p-4">
                <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed bg-gray-50 p-4 rounded-lg border">
                  {previewContent || 'Không có nội dung để hiển thị.'}
                </pre>
              </div>
            </ScrollArea>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
