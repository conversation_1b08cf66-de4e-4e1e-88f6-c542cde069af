"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, FileText, Image as ImageIcon, Download, Eye } from 'lucide-react'

interface FilePreviewModalProps {
  isOpen: boolean
  onClose: () => void
  file: File | null
  fileType: 'image' | 'document' | 'audio' | 'video'
  preview?: string
  content?: string
}

export function FilePreviewModal({
  isOpen,
  onClose,
  file,
  fileType,
  preview,
  content
}: FilePreviewModalProps) {
  const [previewContent, setPreviewContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [pdfPages, setPdfPages] = useState<string[]>([])
  const [pdfCanvases, setPdfCanvases] = useState<string[]>([])
  const [htmlContent, setHtmlContent] = useState<string>('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (isOpen && file && isClient) {
      loadFileContent()
    }
  }, [isOpen, file, isClient])

  const loadFileContent = async () => {
    if (!file) return

    setIsLoading(true)
    setError('')
    setPreviewContent('')
    setPdfPages([])
    setPdfCanvases([])
    setHtmlContent('')

    try {
      if (fileType === 'image') {
        // For images, use the preview URL directly
        setPreviewContent(preview || '')
      } else if (file.type === 'text/plain' || file.type === 'text/csv') {
        // For text files, read directly with original formatting
        const text = await readTextFile(file)
        setPreviewContent(text)
      } else if (file.type === 'application/pdf') {
        // For PDF files, render as visual pages
        await loadPdfContentAsImages(file)
      } else if (file.type.includes('word') || file.type.includes('document')) {
        // For Word documents, convert to HTML with formatting
        await loadWordContentAsHtml(file)
      } else {
        // Fallback to existing content
        setPreviewContent(content || 'Không thể xem trước loại file này.')
      }
    } catch (err) {
      setError(`Lỗi khi tải nội dung file: ${err}`)
    } finally {
      setIsLoading(false)
    }
  }

  const readTextFile = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('Không thể đọc file text'))
      reader.readAsText(file, 'utf-8')
    })
  }

  const loadPdfContentAsImages = async (file: File) => {
    try {
      // Dynamic import PDF.js only when needed
      const pdfjsLib = await import('pdfjs-dist')

      // Use worker from public folder
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf-worker/pdf.worker.min.js'

      const arrayBuffer = await file.arrayBuffer()
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
      const canvases: string[] = []

      // Render first 5 pages to avoid performance issues
      const maxPages = Math.min(pdf.numPages, 5)

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        const page = await pdf.getPage(pageNum)

        // Set up canvas for rendering
        const viewport = page.getViewport({ scale: 1.5 })
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')

        canvas.height = viewport.height
        canvas.width = viewport.width

        // Render page to canvas
        const renderContext = {
          canvasContext: context,
          viewport: viewport
        }

        await page.render(renderContext).promise

        // Convert canvas to data URL
        const dataUrl = canvas.toDataURL('image/png')
        canvases.push(dataUrl)
      }

      setPdfCanvases(canvases)

      if (pdf.numPages > 5) {
        setPreviewContent(`Hiển thị ${maxPages} trang đầu tiên. Tổng cộng ${pdf.numPages} trang.`)
      } else {
        setPreviewContent(`Hiển thị tất cả ${pdf.numPages} trang.`)
      }
    } catch (err) {
      throw new Error(`Không thể đọc PDF: ${err}`)
    }
  }

  const loadWordContentAsHtml = async (file: File) => {
    try {
      // Dynamic import mammoth only when needed
      const mammothLib = await import('mammoth')
      const mammoth = mammothLib.default || mammothLib

      const arrayBuffer = await file.arrayBuffer()

      // Convert to HTML to preserve formatting
      const result = await mammoth.convertToHtml({ arrayBuffer })

      // Set HTML content with preserved formatting
      setHtmlContent(result.value)

      if (result.messages.length > 0) {
        console.warn('Word conversion warnings:', result.messages)
      }
    } catch (err) {
      throw new Error(`Không thể đọc Word document: ${err}`)
    }
  }

  const downloadFile = () => {
    if (!file) return

    const url = URL.createObjectURL(file)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getFileIcon = () => {
    if (fileType === 'image') return <ImageIcon className="h-5 w-5" />
    return <FileText className="h-5 w-5" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Don't render on server-side
  if (!isClient) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-[95vw] h-[90vh] flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getFileIcon()}
              <div>
                <DialogTitle className="text-left">{file?.name}</DialogTitle>
                <p className="text-sm text-muted-foreground">
                  {file && formatFileSize(file.size)} • {file?.type}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={downloadFile}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Tải xuống
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span>Đang tải nội dung...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-red-600 mb-2">{error}</p>
                <Button variant="outline" onClick={loadFileContent}>
                  Thử lại
                </Button>
              </div>
            </div>
          ) : fileType === 'image' && previewContent ? (
            <div className="flex items-center justify-center h-full p-4">
              <img
                src={previewContent}
                alt={file?.name}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              />
            </div>
          ) : file?.type === 'application/pdf' && pdfCanvases.length > 0 ? (
            <div className="flex-1 overflow-auto">
              <div className="p-6">
                <div className="text-sm text-gray-600 mb-6 text-center bg-blue-50 p-3 rounded-lg">
                  {previewContent}
                </div>
                <div className="space-y-6">
                  {pdfCanvases.map((canvasData, index) => (
                    <div key={index} className="border rounded-lg overflow-hidden shadow-md bg-white">
                      <div className="bg-gray-100 px-4 py-3 text-sm font-medium text-gray-700 border-b">
                        📄 Trang {index + 1}
                      </div>
                      <div className="p-6 flex justify-center bg-gray-50">
                        <img
                          src={canvasData}
                          alt={`Trang ${index + 1}`}
                          className="max-w-full h-auto border shadow-lg rounded"
                          style={{ maxHeight: '800px' }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (file?.type.includes('word') || file?.type.includes('document')) && htmlContent ? (
            <div className="flex-1 overflow-auto">
              <div className="p-6">
                <div className="bg-white border rounded-lg shadow-md">
                  <div className="bg-blue-50 px-4 py-3 text-sm font-medium text-gray-700 border-b">
                    📝 Microsoft Word Document
                  </div>
                  <div
                    className="p-8 prose prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: htmlContent }}
                    style={{
                      fontFamily: 'Times New Roman, serif',
                      lineHeight: '1.6',
                      color: '#000',
                      fontSize: '14px'
                    }}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-auto">
              <div className="p-6">
                <div className="bg-white border rounded-lg shadow-md">
                  <div className="bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b">
                    📄 {file?.type === 'text/plain' ? 'Text Document' : 'File Content'}
                  </div>
                  <div className="p-8">
                    <pre
                      className="whitespace-pre-wrap text-sm leading-relaxed"
                      style={{
                        fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                        color: '#000',
                        margin: 0,
                        background: 'transparent'
                      }}
                    >
                      {previewContent || 'Không có nội dung để hiển thị.'}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
