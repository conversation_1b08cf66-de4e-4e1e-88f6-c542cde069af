"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"

interface OfficeViewerProps {
  file: File
  onFallback: () => void
}

export default function OfficeViewer({ file, onFallback }: OfficeViewerProps) {
  const [viewerUrl, setViewerUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    setupViewer()
  }, [file])

  const setupViewer = async () => {
    try {
      setIsLoading(true)
      setError('')

      // Method 1: Try Google Docs Viewer (works with public URLs)
      // Method 2: Try Office Online Viewer
      // Method 3: Fallback to local rendering

      // For local files, we need to upload to a temporary service or use blob URLs
      // Let's try Google Docs Viewer with blob URL
      const fileUrl = URL.createObjectURL(file)
      
      // Google Docs Viewer URL
      const googleViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true`
      
      // Office Online Viewer URL (requires public URL)
      const officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`

      // Try Google Docs Viewer first
      setViewerUrl(googleViewerUrl)
      setIsLoading(false)

    } catch (err) {
      console.error('Error setting up viewer:', err)
      setError('Không thể tải viewer')
      setIsLoading(false)
    }
  }

  const handleIframeError = () => {
    setError('Không thể tải document viewer')
    // Fallback to local rendering
    onFallback()
  }

  const handleIframeLoad = () => {
    setIsLoading(false)
    setError('')
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 p-4">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={onFallback} variant="outline">
          Sử dụng viewer cục bộ
        </Button>
      </div>
    )
  }

  return (
    <div className="h-full w-full relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span>Đang tải Office Viewer...</span>
          </div>
        </div>
      )}
      
      <iframe
        src={viewerUrl}
        className="w-full h-full border-0"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        title="Office Document Viewer"
        sandbox="allow-scripts allow-same-origin allow-popups"
      />
      
      <div className="absolute bottom-4 right-4">
        <Button 
          onClick={onFallback} 
          variant="outline" 
          size="sm"
          className="bg-white shadow-md"
        >
          Viewer cục bộ
        </Button>
      </div>
    </div>
  )
}
